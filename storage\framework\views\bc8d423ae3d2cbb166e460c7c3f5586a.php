<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Manage Products</h1>
        <a href="<?php echo e(route('vendor.products.create')); ?>" class="btn btn-dark">
            <i class="fas fa-plus me-2"></i> Add New Product
        </a>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Stock</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <img src="<?php echo e($product->image_url ?? 'https://via.placeholder.com/50x50?text=No+Image'); ?>" 
                                        alt="<?php echo e($product->name); ?>" width="50" height="50" class="rounded">
                                </td>
                                <td><?php echo e($product->name); ?></td>
                                <td><?php echo e($product->category->name ?? 'N/A'); ?></td>
                                <td>
                                    <?php if($product->discount_price): ?>
                                        <span class="text-decoration-line-through text-muted me-2">$<?php echo e(number_format($product->price, 2)); ?></span>
                                        <span class="fw-bold">$<?php echo e(number_format($product->discount_price, 2)); ?></span>
                                    <?php else: ?>
                                        <span class="fw-bold">$<?php echo e(number_format($product->price, 2)); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($product->stock_quantity > 0): ?>
                                        <span class="badge bg-success"><?php echo e($product->stock_quantity); ?> in stock</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Out of stock</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($product->is_active): ?>
                                        <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="<?php echo e(route('vendor.products.edit', $product->id)); ?>" class="btn btn-sm btn-outline-dark me-2">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('vendor.products.destroy', $product->id)); ?>" method="POST" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this product?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                                        <h5 class="fw-bold mb-2">No Products Yet</h5>
                                        <p class="text-muted mb-3">You haven't added any products to your store.</p>
                                        <a href="<?php echo e(route('vendor.products.create')); ?>" class="btn btn-dark">
                                            <i class="fas fa-plus me-2"></i> Add Your First Product
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-center mt-4">
        <?php echo e($products->links()); ?>

    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.vendor', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Music\brandify\brandifyng\resources\views/vendor/products/index.blade.php ENDPATH**/ ?>